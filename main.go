package main

import (
	"context"
	"fmt"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"fin_work_intent/internal/config"
	"fin_work_intent/internal/grpcserver"
	"fin_work_intent/proto/intent"

	"go.uber.org/zap"
	"google.golang.org/grpc"
)

func main() {
	// Load config
	cfg := config.LoadConfig()

	// Setup logger
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	// Print service banner
	fmt.Printf("\n====================================\n")
	fmt.Printf(" 🚀 %s is starting\n", cfg.AppName)
	fmt.Printf(" 📡 gRPC listening on :%s\n", cfg.GrpcPort)
	fmt.Printf("====================================\n\n")

	// Create listener
	addr := ":" + cfg.GrpcPort
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		logger.Fatal("failed to listen", zap.Error(err))
	}

	grpcServer := grpc.NewServer()
	intentServer := grpcserver.NewIntentServer(logger)
	intent.RegisterIntentServiceServer(grpcServer, intentServer)

	// Setup context with signal handling
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	// Run gRPC in goroutine
	go func() {
		if err := grpcServer.Serve(lis); err != nil {
			logger.Fatal("failed to serve gRPC", zap.Error(err))
		}
	}()

	logger.Info("fin_work_intent started successfully")

	// Wait for shutdown signal
	<-ctx.Done()
	logger.Info("shutdown signal received, stopping server...")

	// Graceful stop
	stopped := make(chan struct{})
	go func() {
		grpcServer.GracefulStop()
		close(stopped)
	}()

	// Timeout for shutdown
	select {
	case <-stopped:
		logger.Info("server stopped gracefully")
	case <-time.After(5 * time.Second):
		logger.Warn("force stopping server after timeout")
		grpcServer.Stop()
	}

	fmt.Println("👋 Goodbye!")
}
