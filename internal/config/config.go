package config

import (
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	AppName  string
	GrpcPort string
	LogLevel string
}

func LoadConfig() *Config {
	_ = godotenv.Load() // load .env kalau ada

	cfg := &Config{
		AppName:  getEnv("APP_NAME", "fin_work_intent"),
		GrpcPort: getEnv("GRPC_PORT", "50051"),
		LogLevel: getEnv("LOG_LEVEL", "info"),
	}

	return cfg
}

func getEnv(key, defaultVal string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultVal
}
