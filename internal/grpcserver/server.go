package grpcserver

import (
	"context"

	intent "fin_work_intent/proto/intent"

	"go.uber.org/zap"
)

type IntentServer struct {
	intent.UnimplementedIntentServiceServer
	logger *zap.Logger
}

func NewIntentServer(logger *zap.Logger) *IntentServer {
	return &IntentServer{logger: logger}
}

func (s *IntentServer) DetectIntent(ctx context.Context, req *intent.IntentRequest) (*intent.IntentResponse, error) {
	s.logger.Info("DetectIntent called",
		zap.String("id", req.Id),
		zap.Int("messages_count", len(req.Messages)),
	)

	// contoh hardcoded response dulu
	resp := &intent.IntentResponse{
		Intent: &intent.Intent{
			Label:      "sql",
			Confidence: 0.85,
			Meta: &intent.IntentMeta{
				Route:       "chat.request.sql",
				Aggregation: "summary",
				Dimensions:  []string{"brand", "year"},
				Metrics:     []string{"count"},
			},
		},
	}

	return resp, nil
}
