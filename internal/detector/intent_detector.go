package detector

import (
	"strings"

	"fin_work_intent/internal/model"
)

type IntentRule struct {
	Label    string
	Route    string
	Meta     model.MetaData
	Keywords []string
}

var rules = []IntentRule{
	{
		Label: "summary",
		Route: "chat.request.summary",
		Meta: model.MetaData{
			Aggregation: "summary",
			Granularity: "year",
			Dimensions:  []string{"brand", "year"},
			Metrics:     []string{"awareness"},
			Entities:    []string{"BHT"},
		},
		Keywords: []string{"summary", "ringkasan", "awareness"},
	},
	{
		Label: "sql",
		Route: "chat.request.sql",
		Meta: model.MetaData{
			Aggregation: "summary",
			Granularity: "year",
			Dimensions:  []string{"brand", "year"},
			Metrics:     []string{"awareness"},
			Entities:    []string{},
		},
		Keywords: []string{"list", "products", "data", "tahun"},
	},
}

func DetectIntent(message string) model.Intent {
	msgLower := strings.ToLower(message)

	bestIntent := model.Intent{
		Label:      "unknown",
		Confidence: 0.0,
		Meta: model.MetaData{
			Route: "chat.request.unknown",
		},
	}

	for _, rule := range rules {
		matched := 0
		for _, kw := range rule.Keywords {
			if strings.Contains(msgLower, strings.ToLower(kw)) {
				matched++
			}
		}

		if matched > 0 {
			conf := float64(matched) / float64(len(rule.Keywords))
			if conf > bestIntent.Confidence {
				bestIntent = model.Intent{
					Label:      rule.Label,
					Confidence: conf,
					Meta: model.MetaData{
						Route:       rule.Route,
						Aggregation: rule.Meta.Aggregation,
						Granularity: rule.Meta.Granularity,
						Dimensions:  rule.Meta.Dimensions,
						Metrics:     rule.Meta.Metrics,
						Entities:    rule.Meta.Entities,
					},
				}
			}
		}
	}

	return bestIntent
}
