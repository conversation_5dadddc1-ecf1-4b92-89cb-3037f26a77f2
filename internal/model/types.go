package model

type Intent struct {
	Label      string   `json:"label"`
	Confidence float64  `json:"confidence"`
	Meta       MetaData `json:"meta"`
}

type MetaData struct {
	Route       string   `json:"route"`
	Aggregation string   `json:"aggregation"`
	Granularity string   `json:"granularity"`
	Dimensions  []string `json:"dimensions"`
	Metrics     []string `json:"metrics"`
	Entities    []string `json:"entities"`
}
